import { type LoaderFunctionArgs, redirect } from 'react-router';

import { isNumericId } from '@shared/lib/validators';

export const courtGridGuard = ({ params }: LoaderFunctionArgs) => {
	const { divisionId, after, before } = params;
	
	// Validate divisionId (must be numeric)
	if (!isNumericId(divisionId)) {
		return redirect('/events');
	}
	
	// Validate after and before timestamps
	if (!after || !before) {
		return redirect('/events');
	}
	
	// Validate that after and before are valid ISO strings or timestamps
	const afterDate = new Date(after);
	const beforeDate = new Date(before);
	
	if (isNaN(afterDate.getTime()) || isNaN(beforeDate.getTime())) {
		return redirect('/events');
	}
	
	// Validate that after is before before
	if (afterDate >= beforeDate) {
		return redirect('/events');
	}
	
	return null;
};
