import { createBrowserRouter, redirect } from 'react-router';

// ─── page components ─────────────────────────────────────────────────────
import { ClubsTeamsPage } from '@pages/clubs-teams';
import { CourtGridPage } from '@pages/court-grid';
import { EventsPage } from '@pages/events';
import { FavoritesPage } from '@pages/favorites';
import { EventLayout } from '@pages/layout';
import { NotFoundPage } from '@pages/not-found';
import { PoolBracketPage } from '@pages/pool-bracket';
import { QualifiedPage } from '@pages/qualified';
import { AthletesPage } from '@pages/roster/athletes';
import { RosterLayout } from '@pages/roster/layout';
import { StaffPage } from '@pages/roster/staff';
import { PoolBracketEntry, StandingsEntry, TeamsEntry } from '@pages/router-stubs';
import { StandingsPage } from '@pages/standings';
import { TeamsPage } from '@pages/teams';

// ─── guards / loaders ────────────────────────────────────────────────────
import { eventGuard, eventsGuard, poolBracketGuard, standingsGuard, teamsGuard } from './guards';

// ─── router tree ─────────────────────────────────────────────────────────
export const router = createBrowserRouter([
	// Home (redirects to events)
	{
		path: '/',
		loader: () => redirect('events'),
	},

	// /events  (tab + ?search)
	{
		path: '/events/:tab?',
		loader: eventsGuard,
		Component: EventsPage,
	},

	// /event/:eswId (all event-scoped pages)
	{
		path: '/event/:eswId',
		loader: eventGuard,
		Component: EventLayout,
		children: [
			{ index: true, loader: () => redirect('favorites') },

			// pages that need only eswId
			{ path: 'favorites', Component: FavoritesPage },
			{ path: 'qualified', Component: QualifiedPage },
			{ path: 'clubs-teams', Component: ClubsTeamsPage },
			{ path: 'court-grid', Component: CourtGridPage },

			// roster
			{
				path: 'roster',
				Component: RosterLayout,
				children: [
					{ index: true, loader: () => redirect('athletes') },
					{ path: 'athletes', Component: AthletesPage },
					{ path: 'staff', Component: StaffPage },
				],
			},

			// ─── canonical share-able paths (ids are on the page path) ─
			{
				path: 'pool-bracket/:divisionId/round/:roundId',
				loader: poolBracketGuard,
				Component: PoolBracketPage,
			},
			{
				path: 'teams/:divisionId',
				loader: teamsGuard,
				Component: TeamsPage,
			},
			{
				path: 'standings/:divisionId',
				loader: standingsGuard,
				Component: StandingsPage,
			},

			// Wildcard routes for entry components
			{ path: 'pool-bracket', Component: PoolBracketEntry },
			{ path: 'pool-bracket/:divisionId?/round/roundId?', Component: PoolBracketEntry },
			{ path: 'pool-bracket/*', Component: PoolBracketEntry },
			{ path: 'teams', Component: TeamsEntry },
			{ path: 'teams/*', Component: TeamsEntry },
			{ path: 'standings', Component: StandingsEntry },
			{ path: 'standings/*', Component: StandingsEntry },
		],
	},

	// catch-all 404
	{ path: '*', Component: NotFoundPage },
]);
