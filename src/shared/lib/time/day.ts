import { type DateArg, toDate } from 'date-fns';

/**
 * Receive anything that represents a moment – a Date, an ISO-8601 string,
 * or a number – and return a Date set to 00 : 00 UTC on the
 * same calendar day.
 */
export function startOfDayUTC(value?: DateArg<Date>): Date {
	const date = value ? toDate(value) : new Date();
	return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()));
}
