import { type DateArg, toDate } from 'date-fns';

/**
 * Receive anything that represents a moment – a Date, an ISO-8601 string,
 * or a number – and return a Date set to 00 : 00 UTC on the
 * same calendar day.
 */
export const startOfDayUTC = (value?: DateArg<Date>): Date => {
	const date = value ? toDate(value) : new Date();
	return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()));
};

export const atTimeOfDayUTC = (day: DateArg<Date>, time: string): Date => {
	const [hours, minutes] = time.split(':').map(Number);
	const date = toDate(day);
	date.setUTCHours(hours, minutes, 0, 0);
	return date;
};
