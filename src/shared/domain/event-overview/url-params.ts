import { DivisionsDaysRangesMap, MatchesTimeRangeRef } from './types';

// /**
//  * Convert URL parameters (dayIndex, startTime, endTime) to display values and Date objects
//  */
// export const convertUrlParamsToValues = (
// 	dayIndex: string,
// 	startTime: string,
// 	endTime: string,
// 	divisionsTimeRangesMap: DivisionsDaysRangesMap<MatchesTimeRangeRef>,
// 	divisionId: string,
// ): {
// 	day: string;
// 	startTime: string;
// 	endTime: string;
// 	after: Date;
// 	before: Date;
// 	dayIndex: number;
// } => {
// 	const divisionTimeRanges = divisionsTimeRangesMap.get(divisionId);
// 	if (!divisionTimeRanges) {
// 		throw new Error('Division not found');
// 	}
//
// 	const days = Array.from(divisionTimeRanges.keys()).sort();
// 	const dayIndexNum = parseInt(dayIndex, 10);
//
// 	if (dayIndexNum < 0 || dayIndexNum >= days.length) {
// 		throw new Error('Invalid day index');
// 	}
//
// 	const day = days[dayIndexNum];
//
// 	// Create Date objects for the widget
// 	const after = new Date(`${day}T${startTime}:00.000Z`);
// 	const before = new Date(`${day}T${endTime}:00.000Z`);
//
// 	if (isNaN(after.getTime()) || isNaN(before.getTime())) {
// 		throw new Error('Invalid time values');
// 	}
//
// 	return {
// 		day,
// 		startTime,
// 		endTime,
// 		after,
// 		before,
// 		dayIndex: dayIndexNum,
// 	};
// };

/**
 * Convert display values to URL parameters (dayIndex, startTime, endTime)
 */
export const convertDisplayValuesToUrlParams = (
	day: string,
	startTime: string,
	endTime: string,
	divisionsTimeRangesMap: DivisionsDaysRangesMap<MatchesTimeRangeRef>,
	divisionId: string,
): {
	dayIndex: string;
	startTime: string;
	endTime: string;
} => {
	const divisionTimeRanges = divisionsTimeRangesMap.get(divisionId);
	if (!divisionTimeRanges) {
		throw new Error('Division not found');
	}

	const days = Array.from(divisionTimeRanges.keys()).sort();
	const dayIndex = days.indexOf(day);

	if (dayIndex === -1) {
		throw new Error('Day not found in division');
	}

	return {
		dayIndex: dayIndex.toString(),
		startTime,
		endTime,
	};
};
