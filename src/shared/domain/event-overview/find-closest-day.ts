import { differenceInCalendarDays } from 'date-fns';

import { startOfDayUTC } from '@shared/lib/time';

/**
 * Finds the day that is closest to "now" from a list of days.
 * Rules:
 *   • Today is in the list → choose today (diff = 0)
 *   • Otherwise distance = days to nearest day.
 *   • Ties favour the *future* (positive diff).
 */
export const findClosestDay = (days: string[]): string | null => {
	if (!days.length) return null;

	// Utilizing UTC as all dates are ignoring timezones and represented as a naive time in milliseconds
	const today = startOfDayUTC();

	let best: { day: string; diff: number } | null = null;

	for (const day of days) {
		const dayDate = startOfDayUTC(day);
		const diff = differenceInCalendarDays(dayDate, today);

		if (
			best === null ||
			Math.abs(diff) < Math.abs(best.diff) ||
			(Math.abs(diff) === Math.abs(best.diff) && diff > 0) // tie → future wins
		) {
			best = { day, diff };
		}
	}

	return best?.day ?? null;
};
