import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';

import { useEventOverview } from '@entities/event-overview';

import { findDivisionById } from '@shared/domain/division';
import {
	ALL_DIVISIONS_KEY,
	INITIAL_TIME_WINDOW,
	buildDivisionsDaysTimeRangesMap,
	findClosestDay,
} from '@shared/domain/event-overview';

export const CourtGridEntry = () => {
	// EswId is guaranteed by the parent route guard
	const { eswId, divisionId, after, before } = useParams();

	const { divisions, loading } = useEventOverview(eswId!);
	const navigate = useNavigate();

	useEffect(() => {
		if (loading || !divisions) return;

		// Determine target division
		const targetDivisionId = divisionId || ALL_DIVISIONS_KEY;
		const targetDivision = divisionId ? findDivisionById(divisions, divisionId) : null;

		// If specific division was requested but not found, redirect to event
		if (divisionId && !targetDivision) {
			navigate(`/event/${eswId}`);
			return;
		}

		// Build divisions days time ranges map
		const divisionsTimeRangesMap = buildDivisionsDaysTimeRangesMap(divisions);
		const divisionTimeRanges = divisionsTimeRangesMap.get(targetDivisionId);

		if (!divisionTimeRanges || divisionTimeRanges.size === 0) {
			navigate(`/event/${eswId}`);
			return;
		}

		// Determine target day and time range
		let targetAfter: string;
		let targetBefore: string;

		if (after && before) {
			// Validate the provided timestamps
			const afterDate = new Date(after);
			const beforeDate = new Date(before);

			if (!isNaN(afterDate.getTime()) && !isNaN(beforeDate.getTime()) && afterDate < beforeDate) {
				targetAfter = after;
				targetBefore = before;
			} else {
				// Invalid timestamps, fall back to default
				const days = Array.from(divisionTimeRanges.keys());
				const closestDay = findClosestDay(days);
				
				if (!closestDay) {
					navigate(`/event/${eswId}`);
					return;
				}

				const timeRange = divisionTimeRanges.get(closestDay);
				if (!timeRange) {
					navigate(`/event/${eswId}`);
					return;
				}

				const startTime = new Date(`${closestDay}T${timeRange.start_time}`);
				const endTime = new Date(startTime.getTime() + INITIAL_TIME_WINDOW);

				targetAfter = startTime.toISOString();
				targetBefore = endTime.toISOString();
			}
		} else {
			// No time parameters provided, use default
			const days = Array.from(divisionTimeRanges.keys());
			const closestDay = findClosestDay(days);
			
			if (!closestDay) {
				navigate(`/event/${eswId}`);
				return;
			}

			const timeRange = divisionTimeRanges.get(closestDay);
			if (!timeRange) {
				navigate(`/event/${eswId}`);
				return;
			}

			const startTime = new Date(`${closestDay}T${timeRange.start_time}`);
			const endTime = new Date(startTime.getTime() + INITIAL_TIME_WINDOW);

			targetAfter = startTime.toISOString();
			targetBefore = endTime.toISOString();
		}

		// Navigate to the canonical URL
		navigate(
			`/event/${eswId}/court-grid/${targetDivisionId}/${targetAfter}/${targetBefore}`,
			{ replace: true },
		);
	}, [loading, divisions, divisionId, after, before, eswId, navigate]);

	if (!divisions || loading) {
		// If the data is still loading, we can show a loading spinner or placeholder
		return <div>Loading...</div>;
	}
	return null;
};
