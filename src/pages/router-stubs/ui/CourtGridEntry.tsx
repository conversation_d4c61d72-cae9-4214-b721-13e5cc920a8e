import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router';

import { useEventOverview } from '@entities/event-overview';

import { findDivisionById } from '@shared/domain/division';
import {
	ALL_DIVISIONS_KEY,
	INITIAL_TIME_WINDOW,
	buildDivisionsDaysTimeRangesMap,
	findClosestDay,
} from '@shared/domain/event-overview';

export const CourtGridEntry = () => {
	// EswId is guaranteed by the parent route guard
	const { eswId, divisionId, dayIndex, startTime, endTime } = useParams();

	const { divisions, loading } = useEventOverview(eswId!);
	const navigate = useNavigate();

	useEffect(() => {
		if (loading || !divisions) return;

		// Determine target division
		const targetDivisionId = divisionId || ALL_DIVISIONS_KEY;
		const targetDivision = divisionId ? findDivisionById(divisions, divisionId) : null;

		// If specific division was requested but not found, redirect to event
		if (divisionId && !targetDivision) {
			navigate(`/event/${eswId}`);
			return;
		}

		// Build divisions days time ranges map
		const divisionsTimeRangesMap = buildDivisionsDaysTimeRangesMap(divisions);
		const divisionTimeRanges = divisionsTimeRangesMap.get(targetDivisionId);

		if (!divisionTimeRanges || divisionTimeRanges.size === 0) {
			navigate(`/event/${eswId}`);
			return;
		}

		// Determine target day and time range
		let targetDayIndex: string;
		let targetStartTime: string;
		let targetEndTime: string;

		const days = Array.from(divisionTimeRanges.keys()).sort();

		if (dayIndex && startTime && endTime) {
			// Validate the provided parameters
			const dayIndexNum = parseInt(dayIndex, 10);
			const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;

			if (
				dayIndexNum >= 0 &&
				dayIndexNum < days.length &&
				timeRegex.test(startTime) &&
				timeRegex.test(endTime)
			) {
				const [startHour, startMin] = startTime.split(':').map(Number);
				const [endHour, endMin] = endTime.split(':').map(Number);
				const startMinutes = startHour * 60 + startMin;
				const endMinutes = endHour * 60 + endMin;

				if (startMinutes < endMinutes) {
					targetDayIndex = dayIndex;
					targetStartTime = startTime;
					targetEndTime = endTime;
				} else {
					// Invalid time range, fall back to default
					const closestDay = findClosestDay(days);
					if (!closestDay) {
						navigate(`/event/${eswId}`);
						return;
					}
					const timeRange = divisionTimeRanges.get(closestDay);
					if (!timeRange) {
						navigate(`/event/${eswId}`);
						return;
					}

					targetDayIndex = days.indexOf(closestDay).toString();
					targetStartTime = timeRange.start_time.substring(0, 5); // HH:mm format

					// Calculate end time as start time + 4 hours
					const startDate = new Date(`2000-01-01T${timeRange.start_time}`);
					const endDate = new Date(startDate.getTime() + INITIAL_TIME_WINDOW);
					targetEndTime = endDate.toTimeString().substring(0, 5); // HH:mm format
				}
			} else {
				// Invalid parameters, fall back to default
				const closestDay = findClosestDay(days);
				if (!closestDay) {
					navigate(`/event/${eswId}`);
					return;
				}
				const timeRange = divisionTimeRanges.get(closestDay);
				if (!timeRange) {
					navigate(`/event/${eswId}`);
					return;
				}

				targetDayIndex = days.indexOf(closestDay).toString();
				targetStartTime = timeRange.start_time.substring(0, 5); // HH:mm format

				// Calculate end time as start time + 4 hours
				const startDate = new Date(`2000-01-01T${timeRange.start_time}`);
				const endDate = new Date(startDate.getTime() + INITIAL_TIME_WINDOW);
				targetEndTime = endDate.toTimeString().substring(0, 5); // HH:mm format
			}
		} else {
			// No parameters provided, use default
			const closestDay = findClosestDay(days);
			if (!closestDay) {
				navigate(`/event/${eswId}`);
				return;
			}
			const timeRange = divisionTimeRanges.get(closestDay);
			if (!timeRange) {
				navigate(`/event/${eswId}`);
				return;
			}

			targetDayIndex = days.indexOf(closestDay).toString();
			targetStartTime = timeRange.start_time.substring(0, 5); // HH:mm format

			// Calculate end time as start time + 4 hours
			const startDate = new Date(`2000-01-01T${timeRange.start_time}`);
			const endDate = new Date(startDate.getTime() + INITIAL_TIME_WINDOW);
			targetEndTime = endDate.toTimeString().substring(0, 5); // HH:mm format
		}

		// Navigate to the canonical URL
		navigate(
			`/event/${eswId}/court-grid/${targetDivisionId}/${targetDayIndex}/${targetStartTime}/${targetEndTime}`,
			{ replace: true },
		);
	}, [loading, divisions, divisionId, dayIndex, startTime, endTime, eswId, navigate]);

	if (!divisions || loading) {
		// If the data is still loading, we can show a loading spinner or placeholder
		return <div>Loading...</div>;
	}
	return null;
};
