import { useEffect, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';

import { useEventOverview } from '@entities/event-overview';

import { findDivisionById } from '@shared/domain/division';
import { ALL_DIVISIONS_KEY, buildDivisionsDaysTimeRangesMap } from '@shared/domain/event-overview';
import { findClosestDay } from '@shared/lib/time';
import { isNumericIndex, isTimeFormat } from '@shared/lib/validators';

export const CourtGridEntry = () => {
	// EswId is guaranteed by the parent route guard
	const { eswId, divisionId, dayIndex, startTime, endTime } = useParams();

	const { divisions, loading } = useEventOverview(eswId!);
	const navigate = useNavigate();

	const targetParams = useMemo(() => {
		if (!divisions) return null;

		const divisionsTimeRangesMap = buildDivisionsDaysTimeRangesMap(divisions);
		const targetDivisionId = findDivisionById(divisions, divisionId)
			? divisionId!
			: ALL_DIVISIONS_KEY;
		const divisionTimeRanges = divisionsTimeRangesMap.get(targetDivisionId)!;
		const days = Array.from(divisionTimeRanges.keys());

		let targetDayIndex = isNumericIndex(dayIndex)
			? parseInt(dayIndex, 10)
			: days.indexOf(findClosestDay(days)!);

		// Ensure targetDayIndex is within bounds
		let targetDay = days[targetDayIndex];
		if (!targetDay) {
			targetDayIndex = 0;
			targetDay = days[targetDayIndex];
		}

		const timeRange = divisionTimeRanges.get(targetDay);
		const targetStartTime = isTimeFormat(startTime) ? startTime : timeRange?.start_time;
		const targetEndTime = isTimeFormat(endTime) ? endTime : timeRange?.end_time;

		return {
			targetDivisionId,
			targetDayIndex: targetDayIndex.toString(),
			targetStartTime,
			targetEndTime,
		};
	}, [divisions, dayIndex, divisionId, endTime, startTime]);

	useEffect(() => {
		if (!targetParams || loading) return;

		const { targetDivisionId, targetDayIndex, targetStartTime, targetEndTime } = targetParams;
		if (targetDivisionId && targetDayIndex && targetStartTime && targetEndTime) {
			navigate(
				`/event/${eswId}/court-grid/${targetDivisionId}/${targetDayIndex}/${targetStartTime}/${targetEndTime}`,
				{ replace: true },
			);
		} else {
			// If any parameter is invalid or missing, redirect to the event overview
			navigate(`/event/${eswId}`);
		}
	}, [targetParams, loading, navigate, eswId]);

	if (!targetParams || loading) {
		return <div>Loading...</div>;
	}

	return null;
};
