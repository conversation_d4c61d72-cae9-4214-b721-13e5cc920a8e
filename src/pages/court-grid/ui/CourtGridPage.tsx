import { useCallback, useMemo, useState } from 'react';
import { useParams } from 'react-router';
import styled from 'styled-components';

import { CourtGridBoardWidget } from '@widgets/court-grid-board';

const CourtGridPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
	height: 100vh;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const GridContainer = styled.div`
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
`;

const TempControls = styled.div`
	display: flex;
	gap: 1rem;
	margin-bottom: 1rem;
	padding: 1rem;
	background: #f8f9fa;
	border-radius: 4px;
	flex-wrap: wrap;
`;

const ControlGroup = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
`;

const Label = styled.label`
	font-size: 12px;
	font-weight: 600;
	color: ${({ theme }) => theme.colors.text};
`;

const Input = styled.input`
	padding: 0.5rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	font-size: 14px;
`;

const Select = styled.select`
	padding: 0.5rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	font-size: 14px;
`;

export const CourtGridPage = () => {
	const { eswId } = useParams();

	// Temporary state for demo purposes
	const [day, setDay] = useState('2024-03-30');
	const [startTime, setStartTime] = useState('08:00');
	const [endTime, setEndTime] = useState('18:00');
	const [divisionId, setDivisionId] = useState<string>('');
	const [focusedMatchId, setFocusedMatchId] = useState<string | null>(null);
	const [gridStep, setGridStep] = useState(30);

	// Convert day and time strings to Date objects for the Demo
	const after = useMemo(() => new Date(`${day}T${startTime}:00.000Z`), [day, startTime]);
	const before = useMemo(() => new Date(`${day}T${endTime}:00.000Z`), [day, endTime]);

	const handleMatchClick = useCallback(
		(matchId: string) => {
			setFocusedMatchId(matchId === focusedMatchId ? null : matchId);
		},
		[focusedMatchId],
	);

	return (
		<CourtGridPageWrapper>
			<Title>Court Grid</Title>

			<TempControls>
				<ControlGroup>
					<Label>Day</Label>
					<Input type="date" value={day} onChange={(e) => setDay(e.target.value)} />
				</ControlGroup>

				<ControlGroup>
					<Label>Start Time</Label>
					<Input type="time" value={startTime} onChange={(e) => setStartTime(e.target.value)} />
				</ControlGroup>

				<ControlGroup>
					<Label>End Time</Label>
					<Input type="time" value={endTime} onChange={(e) => setEndTime(e.target.value)} />
				</ControlGroup>

				<ControlGroup>
					<Label>Division ID (optional)</Label>
					<Input
						type="text"
						value={divisionId}
						onChange={(e) => setDivisionId(e.target.value)}
						placeholder="Leave empty for all divisions"
					/>
				</ControlGroup>

				<ControlGroup>
					<Label>Grid Step (minutes)</Label>
					<Select value={gridStep} onChange={(e) => setGridStep(Number(e.target.value))}>
						<option value={15}>15 minutes</option>
						<option value={30}>30 minutes</option>
						<option value={60}>60 minutes</option>
					</Select>
				</ControlGroup>
			</TempControls>

			<GridContainer>
				<CourtGridBoardWidget
					eswId={eswId!}
					divisionId={divisionId || null}
					after={after}
					before={before}
					focusedMatchId={focusedMatchId}
					gridStep={gridStep}
					onMatchClick={handleMatchClick}
				/>
			</GridContainer>
		</CourtGridPageWrapper>
	);
};
