import { useCallback, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import styled from 'styled-components';

import { CourtGridBoardWidget } from '@widgets/court-grid-board';

import { useEventOverview } from '@entities/event-overview';

import {
	ALL_DIVISIONS_KEY,
	buildDivisionsDaysTimeRangesMap,
	convertDisplayValuesToUrlParams,
} from '@shared/domain/event-overview';
import { atTimeOfDayUTC } from '@shared/lib/time';

const CourtGridPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
	height: 100vh;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const GridContainer = styled.div`
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
`;

const TempControls = styled.div`
	display: flex;
	gap: 1rem;
	margin-bottom: 1rem;
	padding: 1rem;
	background: #f8f9fa;
	border-radius: 4px;
	flex-wrap: wrap;
`;

const ControlGroup = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
`;

const Label = styled.label`
	font-size: 12px;
	font-weight: 600;
	color: ${({ theme }) => theme.colors.text};
`;

const Input = styled.input`
	padding: 0.5rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	font-size: 14px;
`;

const Select = styled.select`
	padding: 0.5rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	font-size: 14px;
`;

export const CourtGridPage = () => {
	const { eswId, divisionId, dayIndex, startTime, endTime } = useParams();
	const { divisions, loading: eventLoading } = useEventOverview(eswId!);
	const navigate = useNavigate();

	// State for UI controls
	const [focusedMatchId, setFocusedMatchId] = useState<string | null>(null);
	const [gridStep, setGridStep] = useState(30);

	const pageParams = useMemo(() => {
		if (!divisions || !divisionId || !dayIndex || !startTime || !endTime) return null;

		const divisionsDaysTimeRangesMap = buildDivisionsDaysTimeRangesMap(divisions);
		const daysTimeRangesMap = divisionsDaysTimeRangesMap.get(divisionId!)!;
		const days = Array.from(daysTimeRangesMap.keys());
		const day = days[+dayIndex!];
		const timeRange = daysTimeRangesMap.get(day)!;

		const after = atTimeOfDayUTC(day, startTime!);
		const before = atTimeOfDayUTC(day, endTime!);
		return {
			days,
			day,
			timeRange,
			after,
			before,
		};
	}, [divisions, dayIndex, divisionId, endTime, startTime]);

	// URL manipulation handlers
	const updateUrl = useCallback(
		(newDay: string, newStartTime: string, newEndTime: string, newDivisionId?: string) => {
			if (!divisionsTimeRangesMap) return;

			try {
				const targetDivisionId = newDivisionId ?? divisionId ?? ALL_DIVISIONS_KEY;
				const {
					dayIndex: newDayIndex,
					startTime: newStartTimeFormatted,
					endTime: newEndTimeFormatted,
				} = convertDisplayValuesToUrlParams(
					newDay,
					newStartTime,
					newEndTime,
					divisionsTimeRangesMap,
					targetDivisionId,
				);

				navigate(
					`/event/${eswId}/court-grid/${targetDivisionId}/${newDayIndex}/${newStartTimeFormatted}/${newEndTimeFormatted}`,
					{
						replace: true,
					},
				);
			} catch (error) {
				console.error('Failed to update URL:', error);
			}
		},
		[eswId, divisionId, navigate, divisionsTimeRangesMap],
	);

	const handleDayChange = useCallback(
		(newDay: string) => {
			updateUrl(newDay, urlValues.startTime, urlValues.endTime);
		},
		[updateUrl, urlValues.startTime, urlValues.endTime],
	);

	const handleStartTimeChange = useCallback(
		(newStartTime: string) => {
			updateUrl(urlValues.day, newStartTime, urlValues.endTime);
		},
		[updateUrl, urlValues.day, urlValues.endTime],
	);

	const handleEndTimeChange = useCallback(
		(newEndTime: string) => {
			updateUrl(urlValues.day, urlValues.startTime, newEndTime);
		},
		[updateUrl, urlValues.day, urlValues.startTime],
	);

	const handleDivisionChange = useCallback(
		(newDivisionId: string) => {
			updateUrl(urlValues.day, urlValues.startTime, urlValues.endTime, newDivisionId);
		},
		[updateUrl, urlValues.day, urlValues.startTime, urlValues.endTime],
	);

	const handleMatchClick = useCallback(
		(matchId: string) => {
			setFocusedMatchId(matchId === focusedMatchId ? null : matchId);
		},
		[focusedMatchId],
	);

	// Show loading state while event data is loading
	if (eventLoading || !divisionsTimeRangesMap) {
		return (
			<CourtGridPageWrapper>
				<Title>Court Grid</Title>
				<div>Loading...</div>
			</CourtGridPageWrapper>
		);
	}

	return (
		<CourtGridPageWrapper>
			<Title>Court Grid</Title>

			<TempControls>
				<ControlGroup>
					<Label>Day</Label>
					<Input
						type="date"
						value={urlValues.day}
						onChange={(e) => handleDayChange(e.target.value)}
					/>
				</ControlGroup>

				<ControlGroup>
					<Label>Start Time</Label>
					<Input
						type="time"
						value={urlValues.startTime}
						onChange={(e) => handleStartTimeChange(e.target.value)}
					/>
				</ControlGroup>

				<ControlGroup>
					<Label>End Time</Label>
					<Input
						type="time"
						value={urlValues.endTime}
						onChange={(e) => handleEndTimeChange(e.target.value)}
					/>
				</ControlGroup>

				<ControlGroup>
					<Label>Division ID</Label>
					<Input
						type="text"
						value={divisionId === ALL_DIVISIONS_KEY ? '' : divisionId || ''}
						onChange={(e) => handleDivisionChange(e.target.value || ALL_DIVISIONS_KEY)}
						placeholder="Leave empty for all divisions"
					/>
				</ControlGroup>

				<ControlGroup>
					<Label>Grid Step (minutes)</Label>
					<Select value={gridStep} onChange={(e) => setGridStep(Number(e.target.value))}>
						<option value={15}>15 minutes</option>
						<option value={30}>30 minutes</option>
						<option value={60}>60 minutes</option>
					</Select>
				</ControlGroup>
			</TempControls>

			<GridContainer>
				<CourtGridBoardWidget
					eswId={eswId!}
					divisionId={divisionId === ALL_DIVISIONS_KEY ? null : divisionId || null}
					after={urlValues.after}
					before={urlValues.before}
					focusedMatchId={focusedMatchId}
					gridStep={gridStep}
					onMatchClick={handleMatchClick}
				/>
			</GridContainer>
		</CourtGridPageWrapper>
	);
};
